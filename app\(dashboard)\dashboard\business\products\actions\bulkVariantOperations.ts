"use server";

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import { z } from "zod";

// Bulk operation types
export type BulkVariantOperation = "enable" | "disable" | "update_base_price" | "update_discounted_price" | "apply_discount";

// Validation schemas
const bulkVariantOperationSchema = z.object({
  variantIds: z.array(z.string().uuid()).min(1, "At least one variant must be selected"),
  operation: z.enum(["enable", "disable", "update_base_price", "update_discounted_price", "apply_discount"]),
  value: z.number().min(0).optional(),
});

export interface BulkVariantOperationResult {
  success: boolean;
  message: string;
  updatedCount: number;
  errors?: string[];
}

export async function bulkUpdateVariants(
  variantIds: string[],
  operation: BulkVariantOperation,
  value?: number
): Promise<BulkVariantOperationResult> {
  try {
    // Validate input
    const validatedData = bulkVariantOperationSchema.parse({
      variantIds,
      operation,
      value,
    });

    const supabase = await createClient();

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return {
        success: false,
        message: "Authentication required",
        updatedCount: 0,
      };
    }

    // Verify all variants belong to the user's business
    const { data: variants, error: variantsError } = await supabase
      .from("product_variants")
      .select(`
        id,
        product_id,
        base_price,
        discounted_price,
        is_available,
        products!inner(business_id)
      `)
      .in("id", validatedData.variantIds);

    if (variantsError) {
      console.error("Error fetching variants:", variantsError);
      return {
        success: false,
        message: "Failed to fetch variants",
        updatedCount: 0,
      };
    }

    if (!variants || variants.length === 0) {
      return {
        success: false,
        message: "No variants found",
        updatedCount: 0,
      };
    }

    // Check if all variants belong to user's business
    const userBusinessIds = new Set();
    for (const variant of variants) {
      const products = variant.products as { business_id?: string };
      if (products?.business_id) {
        userBusinessIds.add(products.business_id);
      }
    }

    // Get user's business
    const { data: userBusiness, error: businessError } = await supabase
      .from("businesses")
      .select("id")
      .eq("user_id", user.id)
      .single();

    if (businessError || !userBusiness) {
      return {
        success: false,
        message: "Business not found",
        updatedCount: 0,
      };
    }

    if (!userBusinessIds.has(userBusiness.id)) {
      return {
        success: false,
        message: "Unauthorized: Variants do not belong to your business",
        updatedCount: 0,
      };
    }

    // Prepare update data based on operation
    let updateData: Record<string, unknown> = {};
    
    switch (validatedData.operation) {
      case "enable":
        updateData = { is_available: true };
        break;
        
      case "disable":
        updateData = { is_available: false };
        break;
        
      case "update_base_price":
        if (validatedData.value === undefined) {
          return {
            success: false,
            message: "Base price value is required",
            updatedCount: 0,
          };
        }
        updateData = { base_price: validatedData.value };
        break;
        
      case "update_discounted_price":
        if (validatedData.value === undefined) {
          return {
            success: false,
            message: "Discounted price value is required",
            updatedCount: 0,
          };
        }
        updateData = { discounted_price: validatedData.value };
        break;
        
      case "apply_discount":
        if (validatedData.value === undefined) {
          return {
            success: false,
            message: "Discount percentage is required",
            updatedCount: 0,
          };
        }
        
        // Apply discount to each variant individually
        const discountPercentage = validatedData.value;
        const updatePromises = variants.map(async (variant: { id: string; base_price?: number }) => {
          if (!variant.base_price) {
            return { success: false, error: `Variant ${variant.id} has no base price` };
          }

          const discountedPrice = variant.base_price * (1 - discountPercentage / 100);

          const { error } = await supabase
            .from("product_variants")
            .update({ discounted_price: discountedPrice })
            .eq("id", variant.id);

          return { success: !error, error: error?.message };
        });

        const results = await Promise.all(updatePromises);
        const successCount = results.filter((r: { success: boolean }) => r.success).length;
        const errors = results.filter((r: { success: boolean }) => !r.success).map((r: { error?: string }) => r.error).filter(Boolean);
        
        // Revalidate paths
        revalidatePath("/dashboard/business/products");
        
        return {
          success: successCount > 0,
          message: `Applied ${discountPercentage}% discount to ${successCount} variant${successCount > 1 ? 's' : ''}`,
          updatedCount: successCount,
          errors: errors.length > 0 ? errors as string[] : undefined,
        };
        
      default:
        return {
          success: false,
          message: "Invalid operation",
          updatedCount: 0,
        };
    }

    // Perform bulk update for non-discount operations
    const { data: updatedVariants, error: updateError } = await supabase
      .from("product_variants")
      .update(updateData)
      .in("id", validatedData.variantIds)
      .select("id");

    if (updateError) {
      console.error("Error updating variants:", updateError);
      return {
        success: false,
        message: "Failed to update variants",
        updatedCount: 0,
      };
    }

    const updatedCount = updatedVariants?.length || 0;

    // Revalidate paths
    revalidatePath("/dashboard/business/products");

    // Generate success message
    let message = "";
    switch (validatedData.operation) {
      case "enable":
        message = `Enabled ${updatedCount} variant${updatedCount > 1 ? 's' : ''}`;
        break;
      case "disable":
        message = `Disabled ${updatedCount} variant${updatedCount > 1 ? 's' : ''}`;
        break;
      case "update_base_price":
        message = `Updated base price for ${updatedCount} variant${updatedCount > 1 ? 's' : ''}`;
        break;
      case "update_discounted_price":
        message = `Updated discounted price for ${updatedCount} variant${updatedCount > 1 ? 's' : ''}`;
        break;
    }

    return {
      success: true,
      message,
      updatedCount,
    };

  } catch (error) {
    console.error("Bulk variant operation error:", error);
    
    if (error instanceof z.ZodError) {
      return {
        success: false,
        message: "Invalid input data",
        updatedCount: 0,
        errors: error.errors.map(e => e.message),
      };
    }

    return {
      success: false,
      message: "An unexpected error occurred",
      updatedCount: 0,
    };
  }
}

// Helper function to validate variant ownership
export async function validateVariantOwnership(variantIds: string[], userId: string): Promise<boolean> {
  try {
    const supabase = await createClient();

    const { data: variants, error } = await supabase
      .from("product_variants")
      .select(`
        id,
        products!inner(
          business_id,
          businesses!inner(user_id)
        )
      `)
      .in("id", variantIds);

    if (error || !variants) {
      return false;
    }

    // Check if all variants belong to the user
    return variants.every((variant: any) =>
      variant.products?.businesses?.user_id === userId
    );

  } catch (_error) {
    return false;
  }
}
